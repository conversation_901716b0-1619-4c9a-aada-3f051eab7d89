/**
 * 简化的慕夏风格平滑滚动控制器
 * 专注于自然流畅的用户体验
 */

class SmoothScrollController {
  constructor() {
    this.currentPage = 1;
    this.totalPages = 6;
    this.isScrolling = false;
    this.scrollThreshold = 30;
    this.scrollContainer = document.querySelector('.horizontal-scroll-container');
    this.indicators = document.querySelectorAll('.indicator-dot');
    this.pages = document.querySelectorAll('.page');
    this.navbarItems = document.querySelectorAll('.navbar-item');
    
    // 优化的自然动画参数
    this.animationDuration = 500; // 更快响应
    this.easeType = 'cubic-bezier(0.23, 1, 0.32, 1)'; // 自然缓动
    
    this.init();
  }
  
  init() {
    this.setupEventListeners();
    this.updatePagePosition();
    this.updateIndicators();
    this.updatePageStates();
    this.updateNavbarActiveState();
    this.addSmoothStyles();
  }
  
  addSmoothStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .horizontal-scroll-container {
        transition: transform ${this.animationDuration}ms ${this.easeType};
        will-change: transform;
      }
      
      .page {
        will-change: opacity, transform;
      }
      
      .page.active {
        opacity: 1;
      }
      
      .page:not(.active) {
        opacity: 0.3;
      }
      
      .indicator-dot {
        transition: all 0.3s ease;
      }
      
      .indicator-dot.active {
        background: #DAA520;
        border-color: #8B4513;
        transform: scale(1.2);
        box-shadow: 0 0 15px rgba(218, 165, 32, 0.6);
      }
    `;
    document.head.appendChild(style);
  }
  
  setupEventListeners() {
    // 滚轮事件
    window.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });
    
    // 键盘事件
    window.addEventListener('keydown', this.handleKeydown.bind(this));
    
    // 指示器点击
    this.indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        this.goToPage(index + 1);
      });
    });
    
    // 触摸事件
    let touchStartX = 0;
    window.addEventListener('touchstart', (e) => {
      touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });
    
    window.addEventListener('touchend', (e) => {
      const touchEndX = e.changedTouches[0].screenX;
      const diff = touchStartX - touchEndX;
      
      if (Math.abs(diff) > 50) {
        if (diff > 0) {
          this.nextPage();
        } else {
          this.prevPage();
        }
      }
    }, { passive: true });
  }
  
  handleWheel(event) {
    event.preventDefault();
    
    if (this.isScrolling) return;
    
    const deltaY = event.deltaY;
    const deltaX = event.deltaX;
    
    if (Math.abs(deltaY) + Math.abs(deltaX) < this.scrollThreshold) return;
    
    if (deltaY > 0 || deltaX > 0) {
      this.nextPage();
    } else {
      this.prevPage();
    }
  }
  
  handleKeydown(event) {
    if (this.isScrolling) return;
    
    switch (event.key) {
      case 'ArrowRight':
      case ' ':
        event.preventDefault();
        this.nextPage();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        this.prevPage();
        break;
      case 'Home':
        event.preventDefault();
        this.goToPage(1);
        break;
      case 'End':
        event.preventDefault();
        this.goToPage(this.totalPages);
        break;
    }
  }
  
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.goToPage(this.currentPage + 1);
    }
  }
  
  prevPage() {
    if (this.currentPage > 1) {
      this.goToPage(this.currentPage - 1);
    }
  }
  
  goToPage(pageNumber) {
    if (pageNumber < 1 || pageNumber > this.totalPages || 
        pageNumber === this.currentPage || this.isScrolling) {
      return;
    }
    
    this.isScrolling = true;
    this.currentPage = pageNumber;
    
    this.updatePagePosition();
    this.updateIndicators();
    this.updatePageStates();
    this.updateNavbarActiveState();
    
    // 简单的完成回调
    setTimeout(() => {
      this.isScrolling = false;
    }, this.animationDuration + 50);
  }
  
  updatePagePosition() {
    const translateX = -(this.currentPage - 1) * 100;
    this.scrollContainer.style.transform = `translateX(${translateX}vw)`;
  }
  
  updateIndicators() {
    this.indicators.forEach((indicator, index) => {
      const isActive = index + 1 === this.currentPage;
      indicator.classList.toggle('active', isActive);
    });
  }
  
  updatePageStates() {
    this.pages.forEach((page, index) => {
      const isActive = index + 1 === this.currentPage;
      page.classList.toggle('active', isActive);
    });
  }
  
  updateNavbarActiveState() {
    this.navbarItems.forEach(item => {
      const page = parseInt(item.getAttribute('data-page'));
      if (page === this.currentPage) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  const smoothScroll = new SmoothScrollController();
  window.smoothScroll = smoothScroll;
});

export default SmoothScrollController;