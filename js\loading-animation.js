/**
 * 慕夏风格月亮加载动画控制模块
 * 控制月亮逐渐变圆的动画，然后过渡到主页内容
 */

document.addEventListener('DOMContentLoaded', function() {
  // 获取加载动画元素
  const loadingOverlay = document.getElementById('loadingOverlay');
  const moonShadow = document.querySelector('.moon-shadow');
  const horizontalScrollContainer = document.querySelector('.horizontal-scroll-container');
  const muchaBorderFrame = document.querySelector('.mucha-border-frame');
  const muchaCursor = document.querySelector('.mucha-cursor');
  const pageIndicator = document.querySelector('.page-indicator');
  
  // 隐藏所有主要内容元素
  if (horizontalScrollContainer) horizontalScrollContainer.style.opacity = '0';
  if (muchaBorderFrame) muchaBorderFrame.style.opacity = '0';
  if (muchaCursor) muchaCursor.style.opacity = '0';
  if (pageIndicator) pageIndicator.style.opacity = '0';
  
  // 确保月亮初始状态是完整的（隐藏阴影）
  if (moonShadow) {
    moonShadow.style.transition = 'transform 2s cubic-bezier(0.23, 1, 0.32, 1)';
  }
  
  // 等待一段时间后开始月亮动画
  setTimeout(function() {
    if (moonShadow) {
      // 月亮变圆的动画
      moonShadow.style.transform = 'translateX(0)';
    }
  }, 300);
  
  // 月亮变圆后，开始隐藏加载动画
  setTimeout(function() {
    // 开始隐藏加载动画
    if (loadingOverlay) {
      loadingOverlay.classList.add('hidden');
    }
    
    // 显示主要内容元素
    setTimeout(function() {
      if (horizontalScrollContainer) {
        horizontalScrollContainer.style.opacity = '1';
        horizontalScrollContainer.style.transition = 'opacity 1s ease-in-out';
      }
      if (muchaBorderFrame) {
        muchaBorderFrame.style.opacity = '1';
        muchaBorderFrame.style.transition = 'opacity 1s ease-in-out';
      }
      if (muchaCursor) {
        muchaCursor.style.opacity = '1';
        muchaCursor.style.transition = 'opacity 1s ease-in-out';
      }
      if (pageIndicator) {
        pageIndicator.style.opacity = '1';
        pageIndicator.style.transition = 'opacity 1s ease-in-out';
      }
      
      // 完全隐藏加载动画
      setTimeout(function() {
        if (loadingOverlay) {
          loadingOverlay.style.display = 'none';
        }
      }, 1000);
    }, 300);
  }, 2300); // 缩短月亮动画时间（从4.5秒减少到2.3秒）
});