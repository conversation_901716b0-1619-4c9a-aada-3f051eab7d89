<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700"/>
      <stop offset="100%" style="stop-color:#B8860B"/>
    </linearGradient>
  </defs>
  
  <!-- 深色背景 -->
  <rect width="32" height="32" rx="6" fill="#1a1a1a"/>
  
  <!-- 装饰边框 -->
  <rect x="2" y="2" width="28" height="28" rx="4" fill="none" stroke="url(#gold)" stroke-width="1" opacity="0.8"/>
  
  <!-- 简化的X字母 -->
  <g stroke="url(#gold)" stroke-width="3" stroke-linecap="round" fill="none">
    <line x1="10" y1="10" x2="22" y2="22"/>
    <line x1="22" y1="10" x2="10" y2="22"/>
  </g>
  
  <!-- 装饰点 -->
  <circle cx="16" cy="6" r="1.5" fill="url(#gold)" opacity="0.7"/>
  <circle cx="16" cy="26" r="1.5" fill="url(#gold)" opacity="0.7"/>
  <circle cx="6" cy="16" r="1.5" fill="url(#gold)" opacity="0.7"/>
  <circle cx="26" cy="16" r="1.5" fill="url(#gold)" opacity="0.7"/>
</svg>
