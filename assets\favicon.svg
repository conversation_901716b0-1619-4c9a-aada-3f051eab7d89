<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="darkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2F4F4F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1C1C1C;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#darkGradient)" stroke="url(#goldGradient)" stroke-width="1"/>
  
  <!-- Art Nouveau装饰边框 -->
  <circle cx="16" cy="16" r="13" fill="none" stroke="url(#goldGradient)" stroke-width="0.5" opacity="0.6"/>
  
  <!-- 装饰性花卉元素 -->
  <path d="M16 4 Q18 6 16 8 Q14 6 16 4" fill="url(#goldGradient)" opacity="0.8"/>
  <path d="M16 28 Q18 26 16 24 Q14 26 16 28" fill="url(#goldGradient)" opacity="0.8"/>
  <path d="M4 16 Q6 14 8 16 Q6 18 4 16" fill="url(#goldGradient)" opacity="0.8"/>
  <path d="M28 16 Q26 14 24 16 Q26 18 28 16" fill="url(#goldGradient)" opacity="0.8"/>
  
  <!-- 中心的装饰性X字母 -->
  <g transform="translate(16,16)">
    <!-- X的主体 -->
    <path d="M-6 -8 L-2 -4 L-6 0 L-4 2 L0 -2 L4 2 L6 0 L2 -4 L6 -8 L4 -6 L0 -2 L-4 -6 Z" 
          fill="url(#goldGradient)" 
          stroke="url(#darkGradient)" 
          stroke-width="0.5"/>
    
    <!-- X的装饰线条 -->
    <path d="M-6 8 L-2 4 L-6 0 L-4 -2 L0 2 L4 -2 L6 0 L2 4 L6 8 L4 6 L0 2 L-4 6 Z" 
          fill="url(#goldGradient)" 
          stroke="url(#darkGradient)" 
          stroke-width="0.5"/>
  </g>
  
  <!-- 细节装饰点 -->
  <circle cx="8" cy="8" r="1" fill="url(#goldGradient)" opacity="0.6"/>
  <circle cx="24" cy="8" r="1" fill="url(#goldGradient)" opacity="0.6"/>
  <circle cx="8" cy="24" r="1" fill="url(#goldGradient)" opacity="0.6"/>
  <circle cx="24" cy="24" r="1" fill="url(#goldGradient)" opacity="0.6"/>
</svg>
