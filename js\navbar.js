/**
 * 慕夏风格固定导航栏控制模块
 * 控制导航栏的交互和页面切换
 */

document.addEventListener('DOMContentLoaded', function() {
  // 获取导航栏元素
  const navbar = document.querySelector('.mucha-navbar');
  const navbarItems = document.querySelectorAll('.navbar-item');
  const pages = document.querySelectorAll('.page');
  const horizontalScrollContainer = document.querySelector('.horizontal-scroll-container');
  
  // 当前页面索引
  let currentPage = 1;
  
  // 滚动时调整导航栏样式
  window.addEventListener('scroll', function() {
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });
  
  // 导航项点击事件
  navbarItems.forEach(item => {
    item.addEventListener('click', function(e) {
      e.preventDefault();
      
      // 获取目标页面
      const targetPage = parseInt(this.getAttribute('data-page'));
      
      // 更新当前页面
      currentPage = targetPage;
      
      // 更新导航栏激活状态
      updateNavbarActiveState();
      
      // 滚动到目标页面
      scrollToPage(targetPage);
    });
  });
  
  // 更新导航栏激活状态
  function updateNavbarActiveState() {
    navbarItems.forEach(item => {
      const page = parseInt(item.getAttribute('data-page'));
      if (page === currentPage) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }
  
  // 滚动到指定页面
  function scrollToPage(pageNumber) {
    if (horizontalScrollContainer) {
      const translateX = -(pageNumber - 1) * 100;
      horizontalScrollContainer.style.transform = `translateX(${translateX}vw)`;
      
      // 更新页面激活状态
      updatePageActiveState(pageNumber);
    }
  }
  
  // 更新页面激活状态
  function updatePageActiveState(pageNumber) {
    pages.forEach((page, index) => {
      if (index + 1 === pageNumber) {
        page.classList.add('active');
      } else {
        page.classList.remove('active');
      }
    });
  }
  
  // 监听水平滚动容器的变换，更新导航栏状态
  if (horizontalScrollContainer) {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const transform = horizontalScrollContainer.style.transform;
          if (transform && transform.includes('translateX')) {
            // 解析当前页面
            const translateValue = transform.match(/translateX\(([^)]+)\)/);
            if (translateValue && translateValue[1]) {
              const translateX = parseFloat(translateValue[1]);
              if (!isNaN(translateX)) {
                const pageIndex = Math.abs(translateX) / 100;
                currentPage = Math.round(pageIndex) + 1;
                updateNavbarActiveState();
                updatePageActiveState(currentPage);
              }
            }
          }
        }
      });
    });
    
    observer.observe(horizontalScrollContainer, {
      attributes: true,
      attributeFilter: ['style']
    });
  }
  
  // 初始化导航栏状态
  updateNavbarActiveState();
  updatePageActiveState(currentPage);
});